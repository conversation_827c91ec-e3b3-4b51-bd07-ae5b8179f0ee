# Enhanced Card Component System

## Overview

The Card component system has been beautifully enhanced using design tokens from our design system and best practices from component libraries like Ant Design Vue and Radix Vue. The enhanced components provide better flexibility, accessibility, and visual appeal.

## Components

### Card (Main Component)

The core Card component with enhanced variants, sizes, and interaction states.

#### Props

```typescript
interface Props {
  variant?: 'default' | 'outlined' | 'elevated' | 'filled' | 'ghost'
  size?: 'sm' | 'md' | 'lg'
  title?: string
  hoverable?: boolean
  loading?: boolean
  bodyStyle?: Record<string, string | number>
  interactive?: boolean
  radius?: 'none' | 'sm' | 'md' | 'lg' | 'xl' | 'full'
}
```

#### Slots

- `header` - Card header content
- `extra` - Extra content in header (top-right)
- `cover` - Cover image/content section
- `default` - Main card content
- `actions` - Action buttons at bottom

#### Variants

- **default**: Clean card with subtle borders and shadows
- **outlined**: Transparent background with strong borders
- **elevated**: Floating appearance with enhanced shadows
- **filled**: Secondary background color
- **ghost**: Minimal card with no background/borders

#### Examples

```vue
<!-- Basic Card -->
<Card>
  <template #header>
    <CardTitle>Basic Card</CardTitle>
  </template>
  <CardContent>
    <p>Simple card content</p>
  </CardContent>
</Card>

<!-- Interactive Card with cover -->
<Card variant="elevated" hoverable interactive>
  <template #cover>
    <img src="image.jpg" alt="Cover" />
  </template>
  <template #header>
    <CardTitle>Interactive Card</CardTitle>
    <template #extra>
      <Badge>Featured</Badge>
    </template>
  </template>
  <CardContent>
    <p>Click anywhere on this card</p>
  </CardContent>
  <template #actions>
    <Button variant="ghost">Cancel</Button>
    <Button>Action</Button>
  </template>
</Card>

<!-- Loading Card -->
<Card :loading="true">
  <CardContent>
    <p>Loading content...</p>
  </CardContent>
</Card>
```

### CardHeader

Enhanced header component with flexible alignment and padding options.

#### Props

```typescript
interface Props {
  padding?: 'none' | 'sm' | 'md' | 'lg'
  align?: 'start' | 'center' | 'between'
}
```

#### Slots

- `default` - Main header content
- `extra` - Extra content (right side)

### CardContent

Enhanced content area with typography styling and flexible padding.

#### Props

```typescript
interface Props {
  padding?: 'none' | 'sm' | 'md' | 'lg'
  style?: Record<string, string | number>
}
```

### CardTitle

Flexible title component with size and weight variants.

#### Props

```typescript
interface Props {
  as?: 'h1' | 'h2' | 'h3' | 'h4' | 'h5' | 'h6' | 'div'
  size?: 'sm' | 'md' | 'lg' | 'xl'
  weight?: 'normal' | 'medium' | 'semibold' | 'bold'
  variant?: 'primary' | 'secondary' | 'tertiary'
}
```

## Features

### 🎨 Design System Integration

- Uses CSS custom properties from design tokens
- Consistent spacing, typography, and colors
- Responsive design with mobile-first approach

### ♿ Accessibility

- Proper focus management and keyboard navigation
- Screen reader friendly with semantic HTML
- High contrast mode support
- Reduced motion support

### ⚡ Performance

- Lightweight with tree-shaking support
- Minimal runtime overhead
- Optimized CSS with custom properties

### 🎯 Interactive States

- Hover effects with smooth animations
- Focus indicators for keyboard users
- Loading states with blur overlay
- Click interactions for interactive cards

### 📱 Responsive

- Mobile-optimized layouts
- Flexible grid system compatibility
- Touch-friendly interactions

### 🌓 Theme Support

- Light/dark mode compatibility
- High contrast mode support
- Customizable via CSS custom properties

## Styling

### CSS Custom Properties

The Card system uses design tokens via CSS custom properties:

```css
/* Available custom properties */
--card-padding: var(--space-4) --card-gap: var(--space-3) --card-shadow-opacity: 0.08
  --card-scale: 1;
```

### Animation Classes

- `animate-float` - Floating animation
- `animate-pulse-slow` - Slow pulse effect
- `animate-fade-in-up` - Fade in from bottom
- `hover-lift` - Hover lift effect

## Best Practices

1. **Use semantic HTML**: The CardTitle component allows choosing appropriate heading levels
2. **Provide alt text**: For cover images and icons
3. **Handle loading states**: Use the loading prop for async content
4. **Focus management**: Ensure interactive cards are keyboard accessible
5. **Content organization**: Use header, content, and actions slots appropriately

## Migration from Old Cards

The enhanced Card system is backward compatible. Existing cards will continue working, but you can gradually adopt new features:

```vue
<!-- Old way -->
<div class="rounded-2xl">
  <slot />
</div>

<!-- New way -->
<Card variant="default" radius="lg">
  <CardContent>
    <slot />
  </CardContent>
</Card>
```

## Browser Support

- Modern browsers (Chrome 80+, Firefox 75+, Safari 13+)
- CSS Grid and Flexbox support required
- CSS Custom Properties support required

## Contributing

When adding new Card features:

1. Follow the existing design token system
2. Ensure accessibility compliance
3. Add appropriate TypeScript types
4. Test across different screen sizes
5. Document new props and slots
