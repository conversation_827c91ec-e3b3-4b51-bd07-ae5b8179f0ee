<template>
  <div class="container mx-auto py-12 px-4">
    <div class="max-w-6xl mx-auto">
      <!-- Title Section -->
      <div class="text-center mb-12">
        <h1 class="text-4xl font-bold text-gray-900 mb-4">Enhanced Card Components</h1>
        <p class="text-xl text-gray-600 max-w-3xl mx-auto">
          Beautified Card components showcasing different variants, sizes, and interactive features
          using our design system.
        </p>
      </div>

      <!-- Basic Card Examples -->
      <section class="mb-16">
        <h2 class="text-2xl font-bold text-gray-900 mb-8">Card Variants</h2>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <!-- Default Card -->
          <Card>
            <template #header>
              <CardTitle>Default Card</CardTitle>
            </template>
            <CardContent>
              <p>This is a default card with subtle shadows and clean borders.</p>
            </CardContent>
          </Card>

          <!-- Outlined Card -->
          <Card variant="outlined" hoverable>
            <template #header>
              <CardTitle>Outlined Card</CardTitle>
            </template>
            <CardContent>
              <p>An outlined card with hover effects and transparent background.</p>
            </CardContent>
          </Card>

          <!-- Elevated Card -->
          <Card variant="elevated" hoverable>
            <template #header>
              <CardTitle>Elevated Card</CardTitle>
            </template>
            <CardContent>
              <p>A floating card with enhanced shadows for depth and dimension.</p>
            </CardContent>
          </Card>

          <!-- Filled Card -->
          <Card variant="filled">
            <template #header>
              <CardTitle>Filled Card</CardTitle>
            </template>
            <CardContent>
              <p>A filled card with secondary background for subtle differentiation.</p>
            </CardContent>
          </Card>

          <!-- Ghost Card -->
          <Card variant="ghost" hoverable>
            <template #header>
              <CardTitle>Ghost Card</CardTitle>
            </template>
            <CardContent>
              <p>A minimal ghost card with no background or borders.</p>
            </CardContent>
          </Card>

          <!-- Interactive Card -->
          <Card interactive radius="lg">
            <template #header>
              <CardTitle>Interactive Card</CardTitle>
            </template>
            <CardContent>
              <p>Click me! This card responds to user interactions.</p>
            </CardContent>
          </Card>
        </div>
      </section>

      <!-- Size Examples -->
      <section class="mb-16">
        <h2 class="text-2xl font-bold text-gray-900 mb-8">Card Sizes</h2>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
          <Card size="sm" variant="outlined">
            <template #header>
              <CardTitle size="sm">Small Card</CardTitle>
            </template>
            <CardContent padding="sm">
              <p>Compact card for minimal content.</p>
            </CardContent>
          </Card>

          <Card size="md" variant="outlined">
            <template #header>
              <CardTitle size="md">Medium Card</CardTitle>
            </template>
            <CardContent padding="md">
              <p>Standard card size for regular content.</p>
            </CardContent>
          </Card>

          <Card size="lg" variant="outlined">
            <template #header>
              <CardTitle size="lg">Large Card</CardTitle>
            </template>
            <CardContent padding="lg">
              <p>Spacious card for detailed content.</p>
            </CardContent>
          </Card>
        </div>
      </section>

      <!-- Advanced Examples -->
      <section class="mb-16">
        <h2 class="text-2xl font-bold text-gray-900 mb-8">Advanced Card Features</h2>
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
          <!-- Card with Cover Image -->
          <Card variant="elevated" hoverable radius="xl">
            <template #cover>
              <div
                class="h-48 bg-gradient-to-br from-blue-400 to-purple-600 flex items-center justify-center"
              >
                <div class="text-white text-center">
                  <h3 class="text-2xl font-bold mb-2">Cover Image</h3>
                  <p>Beautiful gradient background</p>
                </div>
              </div>
            </template>
            <template #header>
              <CardTitle>Card with Cover</CardTitle>
              <template #extra>
                <Badge variant="success">Featured</Badge>
              </template>
            </template>
            <CardContent>
              <p>This card showcases a cover image section with header and extra content.</p>
            </CardContent>
            <template #actions>
              <Button variant="ghost" size="sm">Share</Button>
              <Button variant="default" size="sm">Learn More</Button>
            </template>
          </Card>

          <!-- Loading Card -->
          <Card :loading="true" variant="elevated">
            <template #header>
              <CardTitle>Loading Card</CardTitle>
            </template>
            <CardContent>
              <p>This card demonstrates the loading state with an overlay spinner.</p>
              <p>Content is blurred and non-interactive during loading.</p>
            </CardContent>
            <template #actions>
              <Button variant="default" size="sm">Action</Button>
            </template>
          </Card>
        </div>
      </section>

      <!-- Interactive Examples -->
      <section class="mb-16">
        <h2 class="text-2xl font-bold text-gray-900 mb-8">Interactive Examples</h2>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <!-- Clickable Card -->
          <Card
            interactive
            variant="outlined"
            hoverable
            @click="handleCardClick('Project Card')"
            class="cursor-pointer"
          >
            <template #header>
              <CardTitle>Project Card</CardTitle>
              <template #extra>
                <ArrowRightIcon class="w-5 h-5 text-gray-400" />
              </template>
            </template>
            <CardContent>
              <p>Click anywhere on this card to trigger an action.</p>
              <p class="text-sm text-gray-500 mt-2">Hover to see the lift effect.</p>
            </CardContent>
          </Card>

          <!-- Card with Form -->
          <Card variant="filled">
            <template #header>
              <CardTitle>Contact Form</CardTitle>
            </template>
            <CardContent>
              <form @submit.prevent="handleFormSubmit" class="space-y-4">
                <div>
                  <label for="name" class="block text-sm font-medium text-gray-700 mb-1">
                    Name
                  </label>
                  <input
                    id="name"
                    v-model="formData.name"
                    type="text"
                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="Enter your name"
                  />
                </div>
                <div>
                  <label for="email" class="block text-sm font-medium text-gray-700 mb-1">
                    Email
                  </label>
                  <input
                    id="email"
                    v-model="formData.email"
                    type="email"
                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="Enter your email"
                  />
                </div>
              </form>
            </CardContent>
            <template #actions>
              <Button variant="ghost" size="sm" @click="resetForm">Reset</Button>
              <Button variant="default" size="sm" @click="handleFormSubmit">Submit</Button>
            </template>
          </Card>
        </div>
      </section>

      <!-- Statistics Cards -->
      <section class="mb-16">
        <h2 class="text-2xl font-bold text-gray-900 mb-8">Statistics Dashboard</h2>
        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
          <Card variant="filled" hoverable>
            <CardContent padding="md">
              <div class="flex items-center justify-between">
                <div>
                  <p class="text-sm font-medium text-gray-600">Total Users</p>
                  <p class="text-3xl font-bold text-gray-900">12,345</p>
                </div>
                <div class="bg-blue-100 p-3 rounded-full">
                  <UsersIcon class="w-6 h-6 text-blue-600" />
                </div>
              </div>
              <div class="mt-4 flex items-center">
                <span class="text-green-600 text-sm font-medium">+12%</span>
                <span class="text-gray-500 text-sm ml-2">from last month</span>
              </div>
            </CardContent>
          </Card>

          <Card variant="filled" hoverable>
            <CardContent padding="md">
              <div class="flex items-center justify-between">
                <div>
                  <p class="text-sm font-medium text-gray-600">Revenue</p>
                  <p class="text-3xl font-bold text-gray-900">$54,321</p>
                </div>
                <div class="bg-green-100 p-3 rounded-full">
                  <TrendingUpIcon class="w-6 h-6 text-green-600" />
                </div>
              </div>
              <div class="mt-4 flex items-center">
                <span class="text-green-600 text-sm font-medium">+8%</span>
                <span class="text-gray-500 text-sm ml-2">from last month</span>
              </div>
            </CardContent>
          </Card>

          <Card variant="filled" hoverable>
            <CardContent padding="md">
              <div class="flex items-center justify-between">
                <div>
                  <p class="text-sm font-medium text-gray-600">Projects</p>
                  <p class="text-3xl font-bold text-gray-900">847</p>
                </div>
                <div class="bg-purple-100 p-3 rounded-full">
                  <FolderIcon class="w-6 h-6 text-purple-600" />
                </div>
              </div>
              <div class="mt-4 flex items-center">
                <span class="text-green-600 text-sm font-medium">+23%</span>
                <span class="text-gray-500 text-sm ml-2">from last month</span>
              </div>
            </CardContent>
          </Card>

          <Card variant="filled" hoverable>
            <CardContent padding="md">
              <div class="flex items-center justify-between">
                <div>
                  <p class="text-sm font-medium text-gray-600">Success Rate</p>
                  <p class="text-3xl font-bold text-gray-900">98.5%</p>
                </div>
                <div class="bg-orange-100 p-3 rounded-full">
                  <CheckCircleIcon class="w-6 h-6 text-orange-600" />
                </div>
              </div>
              <div class="mt-4 flex items-center">
                <span class="text-green-600 text-sm font-medium">+2%</span>
                <span class="text-gray-500 text-sm ml-2">from last month</span>
              </div>
            </CardContent>
          </Card>
        </div>
      </section>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'

// Form data
const formData = ref({
  name: '',
  email: '',
})

// Handlers
const handleCardClick = (cardName: string) => {
  alert(`You clicked on ${cardName}!`)
}

const handleFormSubmit = () => {
  if (formData.value.name && formData.value.email) {
    alert(`Form submitted with: ${formData.value.name} (${formData.value.email})`)
  } else {
    alert('Please fill in all fields')
  }
}

const resetForm = () => {
  formData.value = {
    name: '',
    email: '',
  }
}
</script>

<style scoped>
/* Additional animations for the demo */
.animate-gradient {
  background-size: 200% 200%;
  animation: gradient 3s ease infinite;
}

@keyframes gradient {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

/* Custom form styling that works with the cards */
input:focus {
  border-color: var(--color-primary);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Statistics card hover effects */
.grid .card-root:hover .bg-blue-100,
.grid .card-root:hover .bg-green-100,
.grid .card-root:hover .bg-purple-100,
.grid .card-root:hover .bg-orange-100 {
  transform: scale(1.1);
  transition: transform 0.2s ease-in-out;
}
</style>
